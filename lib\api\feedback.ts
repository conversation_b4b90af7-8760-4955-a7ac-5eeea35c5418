import { apiClient, ApiResponse } from './client';
import { API_CONFIG } from '@/lib/config';

// 反馈类型枚举
export enum FeedbackType {
    BUG_REPORT = 'bug_report',
    FEATURE_REQUEST = 'feature_request',
    GENERAL_FEEDBACK = 'general_feedback',
    TECHNICAL_SUPPORT = 'technical_support',
    OTHER = 'other'
}

// 反馈优先级枚举
export enum FeedbackPriority {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    URGENT = 'urgent'
}

// 反馈状态枚举
export enum FeedbackStatus {
    PENDING = 'pending',
    IN_PROGRESS = 'in_progress',
    RESOLVED = 'resolved',
    CLOSED = 'closed'
}

// 创建反馈请求参数
export interface CreateFeedbackParams {
    subject: string;           // 反馈主题
    message: string;          // 反馈内容
    type?: FeedbackType;      // 反馈类型，可选
    priority?: FeedbackPriority; // 优先级，可选
    attachments?: string[];   // 附件URL列表，可选
    user_agent?: string;      // 用户代理信息，可选
    page_url?: string;        // 当前页面URL，可选
}

// 反馈响应数据
export interface FeedbackResponse {
    id: string;
    user_id: string;
    subject: string;
    message: string;
    type: FeedbackType;
    priority: FeedbackPriority;
    status: FeedbackStatus;
    attachments?: string[];
    user_agent?: string;
    page_url?: string;
    created_at: string;
    updated_at: string;
    resolved_at?: string;
}

// 反馈列表响应
export interface FeedbackListResponse {
    feedbacks: FeedbackResponse[];
    total: number;
    page: number;
    limit: number;
}

/**
 * 反馈API服务
 */
export const feedbackApi = {
    /**
     * 创建用户反馈
     * @param params 反馈参数
     * @returns 创建的反馈信息
     */
    createFeedback: async (params: CreateFeedbackParams): Promise<FeedbackResponse> => {
        // 自动添加浏览器和页面信息
        const feedbackData = {
            ...params,
            user_agent: params.user_agent || navigator.userAgent,
            page_url: params.page_url || window.location.href,
            type: params.type || FeedbackType.GENERAL_FEEDBACK,
            priority: params.priority || FeedbackPriority.MEDIUM,
        };

        const response = await apiClient.post<ApiResponse<FeedbackResponse>>(
            '/feedback',
            feedbackData
        );
        return response.data;
    },

    /**
     * 获取用户反馈列表
     * @param page 页码，从1开始
     * @param limit 每页数量
     * @returns 反馈列表
     */
    getUserFeedbacks: async (page: number = 1, limit: number = 10): Promise<FeedbackListResponse> => {
        const params = {
            page: page.toString(),
            limit: limit.toString(),
        };

        const response = await apiClient.get<ApiResponse<FeedbackListResponse>>(
            '/feedback/user',
            { params }
        );
        return response.data;
    },

    /**
     * 获取反馈详情
     * @param feedbackId 反馈ID
     * @returns 反馈详情
     */
    getFeedbackById: async (feedbackId: string): Promise<FeedbackResponse> => {
        const response = await apiClient.get<ApiResponse<FeedbackResponse>>(
            `/feedback/${feedbackId}`
        );
        return response.data;
    },

    /**
     * 更新反馈状态（用户可以关闭自己的反馈）
     * @param feedbackId 反馈ID
     * @param status 新状态
     * @returns 更新后的反馈信息
     */
    updateFeedbackStatus: async (feedbackId: string, status: FeedbackStatus): Promise<FeedbackResponse> => {
        const response = await apiClient.put<ApiResponse<FeedbackResponse>>(
            `/feedback/${feedbackId}/status`,
            { status }
        );
        return response.data;
    },

    /**
     * 删除反馈（用户可以删除自己的反馈）
     * @param feedbackId 反馈ID
     * @returns 操作结果
     */
    deleteFeedback: async (feedbackId: string): Promise<{ success: boolean }> => {
        const response = await apiClient.delete<ApiResponse<{ success: boolean }>>(
            `/feedback/${feedbackId}`
        );
        return response.data;
    },
};
