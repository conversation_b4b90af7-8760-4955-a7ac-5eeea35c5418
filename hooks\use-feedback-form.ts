import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { feedbackApi, CreateFeedbackParams, FeedbackType } from '@/lib/api/feedback';
import { toast } from 'sonner';
import useAuthStore from '@/store/useAuthStore';

// 表单状态接口 - 简化为3个字段
export interface FeedbackFormState {
    type: FeedbackType;
    subject: string;
    message: string;
}

// 表单验证错误接口
export interface FeedbackFormErrors {
    subject?: string;
    message?: string;
}

// 初始表单状态
const initialFormState: FeedbackFormState = {
    type: FeedbackType.BUG,
    subject: '',
    message: '',
};

/**
 * 反馈表单自定义Hook
 * 提供表单状态管理、验证和提交功能
 */
export const useFeedbackForm = () => {
    const [formState, setFormState] = useState<FeedbackFormState>(initialFormState);
    const [errors, setErrors] = useState<FeedbackFormErrors>({});
    const [isSubmitted, setIsSubmitted] = useState(false);

    const { isAuthenticated } = useAuthStore();

    // 创建反馈的mutation - 超级简化
    const createFeedbackMutation = useMutation({
        mutationFn: (params: CreateFeedbackParams) => feedbackApi.createFeedback(params),
        onSuccess: () => {
            setIsSubmitted(true);
            toast.success('Feedback submitted successfully!');

            // 3秒后重置表单
            setTimeout(() => {
                setFormState(initialFormState);
                setErrors({});
                setIsSubmitted(false);
            }, 3000);
        },
        onError: (error: any) => {
            toast.error('Failed to submit feedback. Please try again.');
        },
    });

    /**
     * 验证表单数据 - 简化验证
     */
    const validateForm = (data: FeedbackFormState): FeedbackFormErrors => {
        const newErrors: FeedbackFormErrors = {};

        // 验证主题 - 简化规则
        if (!data.subject.trim()) {
            newErrors.subject = 'Subject is required';
        }

        // 验证消息内容 - 简化规则
        if (!data.message.trim()) {
            newErrors.message = 'Message is required';
        }

        return newErrors;
    };

    /**
     * 处理表单字段变化
     */
    const handleFieldChange = (field: keyof FeedbackFormState, value: string | FeedbackType) => {
        setFormState(prev => ({
            ...prev,
            [field]: value,
        }));

        // 清除对应字段的错误
        if (errors[field as keyof FeedbackFormErrors]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined,
            }));
        }
    };

    /**
     * 处理表单提交 - 超级简化
     */
    const handleSubmit = async (e?: React.FormEvent) => {
        if (e) {
            e.preventDefault();
        }

        // 检查用户是否已登录
        if (!isAuthenticated) {
            toast.error('Please login to submit feedback.');
            return;
        }

        // 验证表单
        const validationErrors = validateForm(formState);
        setErrors(validationErrors);

        // 如果有验证错误，不提交
        if (Object.keys(validationErrors).length > 0) {
            return;
        }

        // 提交表单 - 直接使用表单状态
        createFeedbackMutation.mutate(formState);
    };

    /**
     * 重置表单
     */
    const resetForm = () => {
        setFormState(initialFormState);
        setErrors({});
        setIsSubmitted(false);
    };

    /**
     * 获取字段错误信息
     */
    const getFieldError = (field: keyof FeedbackFormErrors): string | undefined => {
        return errors[field];
    };

    /**
     * 检查字段是否有错误
     */
    const hasFieldError = (field: keyof FeedbackFormErrors): boolean => {
        return !!errors[field];
    };

    /**
     * 检查表单是否有效
     */
    const isFormValid = (): boolean => {
        const validationErrors = validateForm(formState);
        return Object.keys(validationErrors).length === 0;
    };

    return {
        // 表单状态
        formState,
        errors,
        isSubmitted,

        // 提交状态
        isSubmitting: createFeedbackMutation.isPending,
        isSuccess: createFeedbackMutation.isSuccess,
        isError: createFeedbackMutation.isError,

        // 表单操作
        handleFieldChange,
        handleSubmit,
        resetForm,

        // 验证相关
        getFieldError,
        hasFieldError,
        isFormValid,

        // 认证状态
        isAuthenticated,
    };
};
