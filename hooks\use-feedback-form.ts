import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { feedbackApi, CreateFeedbackParams, FeedbackType } from '@/lib/api/feedback';
import { useToast } from '@/components/ui/toast';
import useAuthStore from '@/store/useAuthStore';

// 表单状态接口
export interface FeedbackFormState {
    subject: string;
    message: string;
    type: FeedbackType;
}

// 表单验证错误接口
export interface FeedbackFormErrors {
    subject?: string;
    message?: string;
}

// 初始表单状态
const initialFormState: FeedbackFormState = {
    subject: '',
    message: '',
    type: FeedbackType.GENERAL_FEEDBACK,
};

/**
 * 反馈表单自定义Hook
 * 提供表单状态管理、验证和提交功能
 */
export const useFeedbackForm = () => {
    const [formState, setFormState] = useState<FeedbackFormState>(initialFormState);
    const [errors, setErrors] = useState<FeedbackFormErrors>({});
    const [isSubmitted, setIsSubmitted] = useState(false);
    
    const { success: toastSuccess, error: toastError } = useToast();
    const { isAuthenticated } = useAuthStore();
    const queryClient = useQueryClient();

    // 创建反馈的mutation
    const createFeedbackMutation = useMutation({
        mutationFn: (params: CreateFeedbackParams) => feedbackApi.createFeedback(params),
        onSuccess: (data) => {
            // 成功提交后的处理
            setIsSubmitted(true);
            toastSuccess(
                'Feedback Submitted',
                'Thank you for your feedback! We will review it and get back to you if needed.'
            );
            
            // 清空表单
            setTimeout(() => {
                setFormState(initialFormState);
                setErrors({});
                setIsSubmitted(false);
            }, 3000);

            // 刷新用户反馈列表缓存（如果存在）
            queryClient.invalidateQueries({ queryKey: ['userFeedbacks'] });
        },
        onError: (error: any) => {
            // 错误处理
            const errorMessage = error?.message || 'Failed to submit feedback. Please try again.';
            toastError('Submission Failed', errorMessage);
        },
    });

    /**
     * 验证表单数据
     */
    const validateForm = (data: FeedbackFormState): FeedbackFormErrors => {
        const newErrors: FeedbackFormErrors = {};

        // 验证主题
        if (!data.subject.trim()) {
            newErrors.subject = 'Subject is required';
        } else if (data.subject.trim().length < 5) {
            newErrors.subject = 'Subject must be at least 5 characters long';
        } else if (data.subject.trim().length > 200) {
            newErrors.subject = 'Subject must be less than 200 characters';
        }

        // 验证消息内容
        if (!data.message.trim()) {
            newErrors.message = 'Message is required';
        } else if (data.message.trim().length < 10) {
            newErrors.message = 'Message must be at least 10 characters long';
        } else if (data.message.trim().length > 2000) {
            newErrors.message = 'Message must be less than 2000 characters';
        }

        return newErrors;
    };

    /**
     * 处理表单字段变化
     */
    const handleFieldChange = (field: keyof FeedbackFormState, value: string | FeedbackType) => {
        setFormState(prev => ({
            ...prev,
            [field]: value,
        }));

        // 清除对应字段的错误
        if (errors[field as keyof FeedbackFormErrors]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined,
            }));
        }
    };

    /**
     * 处理表单提交
     */
    const handleSubmit = async (e?: React.FormEvent) => {
        if (e) {
            e.preventDefault();
        }

        // 检查用户是否已登录
        if (!isAuthenticated) {
            toastError('Authentication Required', 'Please login to submit feedback.');
            return;
        }

        // 验证表单
        const validationErrors = validateForm(formState);
        setErrors(validationErrors);

        // 如果有验证错误，不提交
        if (Object.keys(validationErrors).length > 0) {
            toastError('Validation Error', 'Please fix the errors in the form.');
            return;
        }

        // 提交表单
        const submitData: CreateFeedbackParams = {
            subject: formState.subject.trim(),
            message: formState.message.trim(),
            type: formState.type,
        };

        createFeedbackMutation.mutate(submitData);
    };

    /**
     * 重置表单
     */
    const resetForm = () => {
        setFormState(initialFormState);
        setErrors({});
        setIsSubmitted(false);
    };

    /**
     * 获取字段错误信息
     */
    const getFieldError = (field: keyof FeedbackFormErrors): string | undefined => {
        return errors[field];
    };

    /**
     * 检查字段是否有错误
     */
    const hasFieldError = (field: keyof FeedbackFormErrors): boolean => {
        return !!errors[field];
    };

    /**
     * 检查表单是否有效
     */
    const isFormValid = (): boolean => {
        const validationErrors = validateForm(formState);
        return Object.keys(validationErrors).length === 0;
    };

    return {
        // 表单状态
        formState,
        errors,
        isSubmitted,
        
        // 提交状态
        isSubmitting: createFeedbackMutation.isPending,
        isSuccess: createFeedbackMutation.isSuccess,
        isError: createFeedbackMutation.isError,
        
        // 表单操作
        handleFieldChange,
        handleSubmit,
        resetForm,
        
        // 验证相关
        getFieldError,
        hasFieldError,
        isFormValid,
        
        // 认证状态
        isAuthenticated,
    };
};
