import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// 处理POST请求 - 创建反馈
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // 验证用户会话
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ 
        code: 401,
        message: 'Authentication required',
        data: null 
      }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const { subject, message, type, priority, user_agent, page_url } = body;

    // 验证必填字段
    if (!subject || !message) {
      return NextResponse.json({
        code: 400,
        message: 'Subject and message are required',
        data: null
      }, { status: 400 });
    }

    // 转发请求到后端 API
    const response = await fetch(`${process.env.REELAPI_SERVER_URL}/api/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        subject: subject.trim(),
        message: message.trim(),
        type: type || 'general_feedback',
        priority: priority || 'medium',
        user_agent: user_agent || request.headers.get('user-agent'),
        page_url: page_url || request.headers.get('referer'),
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(errorData, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Feedback submission error:', error);
    return NextResponse.json({
      code: 500,
      message: 'Internal server error',
      data: null
    }, { status: 500 });
  }
}

// 处理GET请求 - 获取用户反馈列表
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // 验证用户会话
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ 
        code: 401,
        message: 'Authentication required',
        data: null 
      }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '10';

    // 转发请求到后端 API
    const response = await fetch(`${process.env.REELAPI_SERVER_URL}/api/feedback/user?page=${page}&limit=${limit}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(errorData, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Feedback fetch error:', error);
    return NextResponse.json({
      code: 500,
      message: 'Internal server error',
      data: null
    }, { status: 500 });
  }
}
