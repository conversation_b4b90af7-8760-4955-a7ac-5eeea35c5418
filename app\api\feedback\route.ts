import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// 处理POST请求 - 创建反馈 (超级简化版)
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    // 验证用户会话
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({
        code: 401,
        message: 'Authentication required',
        data: null
      }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const { type, subject, message } = body;

    // 验证必填字段
    if (!type || !subject || !message) {
      return NextResponse.json({
        code: 400,
        message: 'Type, subject and message are required',
        data: null
      }, { status: 400 });
    }

    // 转发请求到后端 API
    const response = await fetch(`${process.env.REELAPI_SERVER_URL}/api/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        type,
        subject: subject.trim(),
        message: message.trim(),
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(errorData, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Feedback submission error:', error);
    return NextResponse.json({
      code: 500,
      message: 'Internal server error',
      data: null
    }, { status: 500 });
  }
}


