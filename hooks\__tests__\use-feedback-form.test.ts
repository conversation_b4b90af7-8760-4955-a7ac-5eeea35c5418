import { renderHook, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useFeedbackForm } from '../use-feedback-form';
import { FeedbackType } from '@/lib/api/feedback';

// Mock the dependencies
jest.mock('@/components/ui/toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
  }),
}));

jest.mock('@/store/useAuthStore', () => ({
  __esModule: true,
  default: () => ({
    isAuthenticated: true,
  }),
}));

jest.mock('@/lib/api/feedback', () => ({
  feedbackApi: {
    createFeedback: jest.fn(),
  },
  FeedbackType: {
    GENERAL_FEEDBACK: 'general_feedback',
    BUG_REPORT: 'bug_report',
    FEATURE_REQUEST: 'feature_request',
    TECHNICAL_SUPPORT: 'technical_support',
    OTHER: 'other',
  },
}));

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useFeedbackForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useFeedbackForm(), {
      wrapper: createWrapper(),
    });

    expect(result.current.formState).toEqual({
      subject: '',
      message: '',
      type: FeedbackType.GENERAL_FEEDBACK,
    });
    expect(result.current.isSubmitting).toBe(false);
    expect(result.current.isSubmitted).toBe(false);
  });

  it('should handle field changes', () => {
    const { result } = renderHook(() => useFeedbackForm(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.handleFieldChange('subject', 'Test Subject');
    });

    expect(result.current.formState.subject).toBe('Test Subject');
  });

  it('should validate form fields', () => {
    const { result } = renderHook(() => useFeedbackForm(), {
      wrapper: createWrapper(),
    });

    // Test empty subject validation
    act(() => {
      result.current.handleFieldChange('subject', '');
      result.current.handleFieldChange('message', 'Valid message content');
    });

    expect(result.current.isFormValid()).toBe(false);

    // Test short subject validation
    act(() => {
      result.current.handleFieldChange('subject', 'Hi');
    });

    expect(result.current.isFormValid()).toBe(false);

    // Test valid form
    act(() => {
      result.current.handleFieldChange('subject', 'Valid Subject');
      result.current.handleFieldChange('message', 'Valid message content');
    });

    expect(result.current.isFormValid()).toBe(true);
  });

  it('should handle form submission', async () => {
    const { result } = renderHook(() => useFeedbackForm(), {
      wrapper: createWrapper(),
    });

    // Set valid form data
    act(() => {
      result.current.handleFieldChange('subject', 'Test Subject');
      result.current.handleFieldChange('message', 'Test message content');
    });

    // Submit form
    await act(async () => {
      await result.current.handleSubmit();
    });

    // Check that the form was submitted (mutation was called)
    expect(result.current.isFormValid()).toBe(true);
  });

  it('should reset form', () => {
    const { result } = renderHook(() => useFeedbackForm(), {
      wrapper: createWrapper(),
    });

    // Set some data
    act(() => {
      result.current.handleFieldChange('subject', 'Test Subject');
      result.current.handleFieldChange('message', 'Test message');
    });

    // Reset form
    act(() => {
      result.current.resetForm();
    });

    expect(result.current.formState).toEqual({
      subject: '',
      message: '',
      type: FeedbackType.GENERAL_FEEDBACK,
    });
  });

  it('should handle field errors', () => {
    const { result } = renderHook(() => useFeedbackForm(), {
      wrapper: createWrapper(),
    });

    // Trigger validation by attempting to submit empty form
    act(() => {
      result.current.handleSubmit();
    });

    expect(result.current.hasFieldError('subject')).toBe(true);
    expect(result.current.hasFieldError('message')).toBe(true);
    expect(result.current.getFieldError('subject')).toBe('Subject is required');
    expect(result.current.getFieldError('message')).toBe('Message is required');
  });
});
